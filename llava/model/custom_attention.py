#    Copyright 2023 <PERSON><PERSON><PERSON> <PERSON>
#
#    Licensed under the Apache License, Version 2.0 (the "License");
#    you may not use this file except in compliance with the License.
#    You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple


class RelationalAttention(nn.Module):
    """
    支持关系位置偏置的自定义注意力机制
    
    该模块在标准的多头注意力基础上，增加了对空间关系偏置的支持，
    能够更好地建模视觉token之间的空间关系。
    
    核心改进：
    1. 在注意力分数计算后注入关系位置偏置
    2. 支持可选的关系偏置缩放
    3. 保持与标准注意力的兼容性
    """
    
    def __init__(self, hidden_size, num_attention_heads, attention_dropout=0.1, 
                 bias_scale=1.0, use_bias_scaling=True):
        """
        初始化关系注意力模块
        
        Args:
            hidden_size (int): 隐藏层维度
            num_attention_heads (int): 注意力头数量
            attention_dropout (float): 注意力dropout概率
            bias_scale (float): 关系偏置的缩放因子
            use_bias_scaling (bool): 是否使用可学习的偏置缩放
        """
        super().__init__()
        
        if hidden_size % num_attention_heads != 0:
            raise ValueError(f"hidden_size ({hidden_size}) 必须能被 num_attention_heads ({num_attention_heads}) 整除")
        
        self.hidden_size = hidden_size
        self.num_attention_heads = num_attention_heads
        self.head_dim = hidden_size // num_attention_heads
        self.scale = 1.0 / math.sqrt(self.head_dim)
        self.bias_scale = bias_scale
        
        # Q, K, V 投影层
        self.q_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.k_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.v_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        
        # 输出投影层
        self.out_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        
        # Dropout
        self.attention_dropout = nn.Dropout(attention_dropout)
        
        # 可学习的偏置缩放参数
        if use_bias_scaling:
            self.bias_scale_param = nn.Parameter(torch.ones(1) * bias_scale)
        else:
            self.register_buffer('bias_scale_param', torch.tensor(bias_scale))
        
        self._init_weights()
        
        print(f"[RelationalAttention] 初始化完成:")
        print(f"  - 隐藏维度: {hidden_size}")
        print(f"  - 注意力头数: {num_attention_heads}")
        print(f"  - 头维度: {self.head_dim}")
        print(f"  - 偏置缩放: {bias_scale}")

    def _init_weights(self):
        """权重初始化"""
        # 使用Xavier初始化
        for module in [self.q_proj, self.k_proj, self.v_proj, self.out_proj]:
            nn.init.xavier_uniform_(module.weight)

    def _reshape_for_attention(self, tensor, batch_size, seq_len):
        """
        将张量重塑为多头注意力的形状
        
        Args:
            tensor: 输入张量 [batch_size, seq_len, hidden_size]
            batch_size: 批次大小
            seq_len: 序列长度
            
        Returns:
            重塑后的张量 [batch_size, num_heads, seq_len, head_dim]
        """
        return tensor.view(batch_size, seq_len, self.num_attention_heads, self.head_dim).transpose(1, 2)

    def forward(self, hidden_states, attention_mask=None, relational_bias=None, 
                output_attentions=False):
        """
        前向传播
        
        Args:
            hidden_states (torch.Tensor): 输入隐藏状态 [batch_size, seq_len, hidden_size]
            attention_mask (torch.Tensor, optional): 注意力掩码
            relational_bias (torch.Tensor, optional): 关系位置偏置 [1, num_heads, seq_len, seq_len]
            output_attentions (bool): 是否输出注意力权重
            
        Returns:
            tuple: (attention_output, attention_weights) 如果 output_attentions=True
                   否则只返回 attention_output
        """
        batch_size, seq_len, _ = hidden_states.shape
        
        # 1. 计算 Q, K, V
        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)
        
        # 2. 重塑为多头注意力形状
        query_states = self._reshape_for_attention(query_states, batch_size, seq_len)
        key_states = self._reshape_for_attention(key_states, batch_size, seq_len)
        value_states = self._reshape_for_attention(value_states, batch_size, seq_len)
        
        # 3. 计算注意力分数
        attention_scores = torch.matmul(query_states, key_states.transpose(-1, -2))
        attention_scores = attention_scores * self.scale
        
        # 4. 注入关系位置偏置（核心创新点）
        if relational_bias is not None:
            # 确保偏置的形状正确
            if relational_bias.dim() == 4:  # [1, num_heads, seq_len, seq_len]
                if relational_bias.shape[1] == 1 and self.num_attention_heads > 1:
                    # 如果偏置只有一个头，扩展到所有头
                    relational_bias = relational_bias.expand(-1, self.num_attention_heads, -1, -1)
                elif relational_bias.shape[1] != self.num_attention_heads:
                    raise ValueError(f"关系偏置的头数 ({relational_bias.shape[1]}) 与注意力头数 ({self.num_attention_heads}) 不匹配")
                
                # 应用偏置缩放并添加到注意力分数
                scaled_bias = relational_bias * self.bias_scale_param
                attention_scores = attention_scores + scaled_bias
            else:
                raise ValueError(f"关系偏置的形状应为 [1, num_heads, seq_len, seq_len]，但得到 {relational_bias.shape}")
        
        # 5. 应用注意力掩码
        if attention_mask is not None:
            # 将掩码转换为大负数，使softmax后接近0
            attention_scores = attention_scores + attention_mask
        
        # 6. 计算注意力权重
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.attention_dropout(attention_weights)
        
        # 7. 计算注意力输出
        attention_output = torch.matmul(attention_weights, value_states)
        
        # 8. 重塑回原始形状
        attention_output = attention_output.transpose(1, 2).contiguous()
        attention_output = attention_output.view(batch_size, seq_len, self.hidden_size)
        
        # 9. 输出投影
        attention_output = self.out_proj(attention_output)
        
        if output_attentions:
            return attention_output, attention_weights
        else:
            return attention_output


class SpatialTransformerLayer(nn.Module):
    """
    空间流专用的Transformer层
    
    该层专门为空间流设计，集成了关系注意力机制和标准的FFN。
    """
    
    def __init__(self, hidden_size, num_attention_heads, intermediate_size=None, 
                 attention_dropout=0.1, hidden_dropout=0.1, layer_norm_eps=1e-5):
        """
        初始化空间Transformer层
        
        Args:
            hidden_size (int): 隐藏层维度
            num_attention_heads (int): 注意力头数量
            intermediate_size (int): FFN中间层维度，默认为4*hidden_size
            attention_dropout (float): 注意力dropout概率
            hidden_dropout (float): 隐藏层dropout概率
            layer_norm_eps (float): LayerNorm的epsilon值
        """
        super().__init__()
        
        if intermediate_size is None:
            intermediate_size = 4 * hidden_size
        
        # 关系注意力层
        self.attention = RelationalAttention(
            hidden_size=hidden_size,
            num_attention_heads=num_attention_heads,
            attention_dropout=attention_dropout
        )
        
        # Layer Normalization
        self.attention_norm = nn.LayerNorm(hidden_size, eps=layer_norm_eps)
        self.ffn_norm = nn.LayerNorm(hidden_size, eps=layer_norm_eps)
        
        # Feed-Forward Network
        self.ffn = nn.Sequential(
            nn.Linear(hidden_size, intermediate_size),
            nn.GELU(),
            nn.Dropout(hidden_dropout),
            nn.Linear(intermediate_size, hidden_size),
            nn.Dropout(hidden_dropout)
        )
        
        print(f"[SpatialTransformerLayer] 初始化完成:")
        print(f"  - 隐藏维度: {hidden_size}")
        print(f"  - FFN中间维度: {intermediate_size}")

    def forward(self, hidden_states, attention_mask=None, relational_bias=None):
        """
        前向传播
        
        Args:
            hidden_states (torch.Tensor): 输入隐藏状态
            attention_mask (torch.Tensor, optional): 注意力掩码
            relational_bias (torch.Tensor, optional): 关系位置偏置
            
        Returns:
            torch.Tensor: 输出隐藏状态
        """
        # 1. 关系注意力 + 残差连接
        residual = hidden_states
        hidden_states = self.attention_norm(hidden_states)
        attention_output = self.attention(
            hidden_states, 
            attention_mask=attention_mask, 
            relational_bias=relational_bias
        )
        hidden_states = residual + attention_output
        
        # 2. FFN + 残差连接
        residual = hidden_states
        hidden_states = self.ffn_norm(hidden_states)
        ffn_output = self.ffn(hidden_states)
        hidden_states = residual + ffn_output
        
        return hidden_states
