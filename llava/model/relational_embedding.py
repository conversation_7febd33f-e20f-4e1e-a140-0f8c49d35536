#    Copyright 2023 <PERSON><PERSON><PERSON>
#
#    Licensed under the Apache License, Version 2.0 (the "License");
#    you may not use this file except in compliance with the License.
#    You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.

import torch
import torch.nn as nn
import math


class RelationalPositionalEncoding(nn.Module):
    """
    显式关系位置编码模块
    
    该模块计算视觉token之间的空间关系，并将其编码为可学习的偏置，
    用于增强多模态模型中的空间理解能力。
    
    核心思想：
    1. 将2D空间中的相对位置关系离散化为有限的关系类别
    2. 为每种关系类别学习一个嵌入向量
    3. 在注意力计算中注入这些关系偏置
    """
    
    def __init__(self, num_bins_per_axis=3, head_dim=64, max_distance=None):
        """
        初始化关系位置编码模块
        
        Args:
            num_bins_per_axis (int): 每个轴向的离散化区间数，默认3（左/中/右，上/中/下）
            head_dim (int): 注意力头的维度
            max_distance (int): 最大距离阈值，超过此距离的关系将被归为边界类别
        """
        super().__init__()
        self.num_bins_per_axis = num_bins_per_axis
        self.total_bins = num_bins_per_axis ** 2  # 总共9种关系类别
        self.head_dim = head_dim
        self.max_distance = max_distance
        
        # 核心：可学习的关系嵌入层
        # 将9种空间关系（如左上、正上、右上等）映射到向量空间
        self.relational_embedding = nn.Embedding(self.total_bins, head_dim)
        
        # 使用Xavier初始化来稳定训练
        nn.init.xavier_uniform_(self.relational_embedding.weight)
        
        print(f"[RelationalPositionalEncoding] 初始化完成:")
        print(f"  - 关系类别数: {self.total_bins}")
        print(f"  - 头维度: {head_dim}")
        print(f"  - 最大距离: {max_distance}")

    def _get_patch_coordinates(self, num_patches_per_side):
        """
        生成ViT patch的2D坐标
        
        Args:
            num_patches_per_side (int): 每边的patch数量（假设为正方形）
            
        Returns:
            torch.Tensor: 形状为[num_patches, 2]的坐标张量
        """
        coords = []
        for i in range(num_patches_per_side):
            for j in range(num_patches_per_side):
                coords.append([i, j])
        return torch.tensor(coords, dtype=torch.float32)

    def _calculate_relational_ids(self, coords):
        """
        计算token对之间的关系ID
        
        Args:
            coords (torch.Tensor): 形状为[num_tokens, 2]的坐标张量
            
        Returns:
            torch.Tensor: 形状为[num_tokens, num_tokens]的关系ID矩阵
        """
        # 计算所有token对之间的坐标差
        # coords.unsqueeze(1): [N, 1, 2]
        # coords.unsqueeze(0): [1, N, 2]
        # delta_coords: [N, N, 2]
        delta_coords = coords.unsqueeze(1) - coords.unsqueeze(0)
        delta_x = delta_coords[..., 0]  # [N, N]
        delta_y = delta_coords[..., 1]  # [N, N]
        
        # 离散化坐标差
        if self.max_distance is not None:
            # 限制最大距离，避免过于稀疏的关系
            delta_x = torch.clamp(delta_x, -self.max_distance, self.max_distance)
            delta_y = torch.clamp(delta_y, -self.max_distance, self.max_distance)
        
        # 将连续的坐标差映射到离散的区间
        # sign函数: 负数->-1, 零->0, 正数->1
        # 加1后: 负数->0, 零->1, 正数->2
        x_bins = torch.sign(delta_x).long() + 1  # [N, N], 值域[0, 1, 2]
        y_bins = torch.sign(delta_y).long() + 1  # [N, N], 值域[0, 1, 2]
        
        # 将2D关系编码为1D的唯一ID
        # 例如: (0,0)->0, (0,1)->1, (0,2)->2, (1,0)->3, ..., (2,2)->8
        relational_ids = y_bins * self.num_bins_per_axis + x_bins
        
        return relational_ids

    def forward(self, coords, num_heads):
        """
        前向传播：计算关系位置偏置
        
        Args:
            coords (torch.Tensor): token坐标，形状[num_tokens, 2]
            num_heads (int): 注意力头的数量
            
        Returns:
            torch.Tensor: 关系位置偏置，形状[1, num_heads, num_tokens, num_tokens]
        """
        device = coords.device
        num_tokens = coords.shape[0]
        
        # 1. 计算关系ID矩阵
        relational_ids = self._calculate_relational_ids(coords)  # [N, N]
        
        # 2. 从嵌入层获取关系向量
        # relational_ids: [N, N] -> [N, N, head_dim]
        bias_vectors = self.relational_embedding(relational_ids)
        
        # 3. 转换为注意力偏置的正确形状
        # 我们需要将[N, N, head_dim]转换为[1, num_heads, N, N]
        # 这里我们假设每个头使用相同的关系偏置，但可以扩展为每头不同的偏置
        
        if self.head_dim == 1:
            # 如果head_dim=1，直接使用标量偏置
            bias = bias_vectors.squeeze(-1)  # [N, N]
            bias = bias.unsqueeze(0).unsqueeze(0)  # [1, 1, N, N]
            bias = bias.expand(1, num_heads, num_tokens, num_tokens)
        else:
            # 如果head_dim > 1，我们需要将向量转换为标量
            # 这里使用简单的平均，也可以使用学习的线性层
            bias = bias_vectors.mean(dim=-1)  # [N, N]
            bias = bias.unsqueeze(0).unsqueeze(0)  # [1, 1, N, N]
            bias = bias.expand(1, num_heads, num_tokens, num_tokens)
        
        return bias.to(device)

    def get_coordinates_from_vision_tower(self, vision_tower, batch_size=1):
        """
        从视觉编码器获取patch坐标
        
        Args:
            vision_tower: 视觉编码器实例
            batch_size (int): 批次大小
            
        Returns:
            torch.Tensor: patch坐标张量
        """
        num_patches_per_side = vision_tower.num_patches_per_side
        coords = self._get_patch_coordinates(num_patches_per_side)
        
        # 如果需要批次维度，可以扩展
        if batch_size > 1:
            coords = coords.unsqueeze(0).expand(batch_size, -1, -1)
        
        return coords


class RelationalPositionalEncodingV2(nn.Module):
    """
    增强版关系位置编码
    
    相比V1版本的改进：
    1. 支持更细粒度的距离编码
    2. 支持可学习的距离权重
    3. 支持不同注意力头的独立关系偏置
    """
    
    def __init__(self, head_dim=64, num_heads=8, max_distance=7, use_head_specific=False):
        super().__init__()
        self.head_dim = head_dim
        self.num_heads = num_heads
        self.max_distance = max_distance
        self.use_head_specific = use_head_specific
        
        # 距离编码：为每个可能的距离学习一个嵌入
        self.distance_embedding = nn.Embedding(2 * max_distance + 1, head_dim)
        
        if use_head_specific:
            # 每个注意力头有独立的关系偏置
            self.head_projection = nn.Linear(head_dim, num_heads)
        
        self._init_weights()
    
    def _init_weights(self):
        """权重初始化"""
        nn.init.xavier_uniform_(self.distance_embedding.weight)
        if hasattr(self, 'head_projection'):
            nn.init.xavier_uniform_(self.head_projection.weight)
            nn.init.zeros_(self.head_projection.bias)
    
    def forward(self, coords, num_heads=None):
        """
        前向传播
        
        Args:
            coords (torch.Tensor): 坐标张量 [num_tokens, 2]
            num_heads (int): 注意力头数量
            
        Returns:
            torch.Tensor: 关系偏置 [1, num_heads, num_tokens, num_tokens]
        """
        if num_heads is None:
            num_heads = self.num_heads
            
        device = coords.device
        num_tokens = coords.shape[0]
        
        # 计算相对距离
        delta_coords = coords.unsqueeze(1) - coords.unsqueeze(0)  # [N, N, 2]
        
        # 分别处理x和y方向的距离
        delta_x = torch.clamp(delta_coords[..., 0], -self.max_distance, self.max_distance)
        delta_y = torch.clamp(delta_coords[..., 1], -self.max_distance, self.max_distance)
        
        # 将距离映射到嵌入索引 [-max_distance, max_distance] -> [0, 2*max_distance]
        x_indices = (delta_x + self.max_distance).long()
        y_indices = (delta_y + self.max_distance).long()
        
        # 获取x和y方向的嵌入
        x_embeddings = self.distance_embedding(x_indices)  # [N, N, head_dim]
        y_embeddings = self.distance_embedding(y_indices)  # [N, N, head_dim]
        
        # 组合x和y方向的信息
        combined_embeddings = x_embeddings + y_embeddings  # [N, N, head_dim]
        
        if self.use_head_specific:
            # 为每个头生成特定的偏置
            bias = self.head_projection(combined_embeddings)  # [N, N, num_heads]
            bias = bias.permute(2, 0, 1).unsqueeze(0)  # [1, num_heads, N, N]
        else:
            # 所有头共享相同的偏置
            bias = combined_embeddings.mean(dim=-1)  # [N, N]
            bias = bias.unsqueeze(0).unsqueeze(0)  # [1, 1, N, N]
            bias = bias.expand(1, num_heads, num_tokens, num_tokens)
        
        return bias.to(device)
